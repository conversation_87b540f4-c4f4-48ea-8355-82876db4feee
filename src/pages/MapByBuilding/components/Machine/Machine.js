import cn from 'classnames'
import { func, number, oneOf, oneOfType, shape, string } from 'prop-types'
import { useEffect, useRef, useState } from 'react'

import { MACHINE } from 'constants/models/machine'
import { useTranslation } from 'hooks/useTranslation'

export const Machine = ({
  machine: {
    index = 0,
    type = MACHINE.TYPE.WASHER,
    status = MACHINE.STATUS.AVAILABLE,
    remainingTime = 0,
  } = {},
  machine,
  onAvailableSelected,
}) => {
  const onClick = () => {
    if (status !== MACHINE.STATUS.AVAILABLE) return

    onAvailableSelected?.(machine)
  }

  const isInUseOrInMaintenance =
    status === MACHINE.STATUS.IN_USE || status === MACHINE.STATUS.IN_MAINTENANCE

  const boxClasses = cn(
    'border-4 rounded-md flex flex-col justify-between h-20',
    {
      'border-lightSilver bg-lightSilver': isInUseOrInMaintenance,
    },
    {
      [MACHINE.TYPE.WASHER]: {
        'border-skyblue': status === MACHINE.STATUS.AVAILABLE,
      },
      [MACHINE.TYPE.DRYER]: {
        'border-blue': status === MACHINE.STATUS.AVAILABLE,
      },
    }[type]
  )

  const dotClasses = cn(
    'w-1.5 h-1.5 rounded-full',
    {
      'bg-white': isInUseOrInMaintenance,
    },
    {
      [MACHINE.TYPE.WASHER]: {
        'bg-skyblue': status === MACHINE.STATUS.AVAILABLE,
      },
      [MACHINE.TYPE.DRYER]: {
        'bg-blue': status === MACHINE.STATUS.AVAILABLE,
      },
    }[type]
  )

  const numberClasses = cn(
    'text-center leading-snug font-light w-full px-1',
    {
      'lm__font--60': !isInUseOrInMaintenance,
      'lm__font--40': isInUseOrInMaintenance,
    },
    {
      [MACHINE.TYPE.WASHER]: {
        'text-skyblue':
          status === MACHINE.STATUS.AVAILABLE ||
          status === MACHINE.STATUS.IN_USE ||
          status === MACHINE.STATUS.IN_MAINTENANCE,
      },
      [MACHINE.TYPE.DRYER]: {
        'text-blue':
          status === MACHINE.STATUS.AVAILABLE ||
          status === MACHINE.STATUS.IN_USE ||
          status === MACHINE.STATUS.IN_MAINTENANCE,
      },
    }[type]
  )

  return (
    <button className={boxClasses} onClick={onClick}>
      <div className='flex m-1'>
        <span className={`${dotClasses} mr-1`} aria-hidden='true' />
        <span className={dotClasses} aria-hidden='true' />
      </div>
      <p className={numberClasses}>
        {isInUseOrInMaintenance ? (
          <RemainingTime initialTime={remainingTime} />
        ) : (
          index
        )}
      </p>
    </button>
  )
}

Machine.propTypes = {
  machine: shape({
    index: oneOfType([string, number]),
    type: oneOf(Object.values(MACHINE.TYPE)),
    status: oneOf(Object.values(MACHINE.STATUS)),
    remainingTime: number,
  }),
  onAvailableSelected: func,
}

const RemainingTime = ({ initialTime }) => {
  const { t } = useTranslation()
  const [timeLeft, setTimeLeft] = useState(initialTime)
  const timerRef = useRef(null)

  useEffect(() => {
    if (timeLeft <= 0) {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      return
    }

    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => Math.max(0, prev - 1000))
    }, 1000)

    return () => {
      if (timerRef.current) clearInterval(timerRef.current)
    }
  }, [timeLeft])

  // Calculate total minutes and seconds from the duration
  const totalMinutes = Math.floor(timeLeft / (1000 * 60))
  const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000)

  // Format seconds with leading zero if needed
  const formattedSeconds = seconds.toString().padStart(2, '0')

  return t('mapByBuilding.machine.remainingTimeNoHour', {
    minute: totalMinutes,
    second: formattedSeconds,
  })
}
